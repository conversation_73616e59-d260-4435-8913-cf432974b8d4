import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

const BRIGHTNESS_DEFAULTS = {
  DEFAULT_BRIGHTNESS: 100,
  MIN_BRIGHTNESS: 0,
  MAX_BRIGHTNESS: 200,
  THROTTLE_DELAY: 100,
};

const useBrightnessStore = create(
  devtools(
    (set, get) => ({

      brightness: BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS,
      participantBrightness: new Map(),
      lastBrightnessUpdate: 0,
      debugMode: process.env.NODE_ENV === 'development',

      // ===== ACTIONS =====
      setBrightness: (value, skipThrottle = false) => {
        const now = Date.now();
        const { lastBrightnessUpdate, debugMode } = get();
        
        // Throttle protection
        if (!skipThrottle && now - lastBrightnessUpdate < BRIGHTNESS_DEFAULTS.THROTTLE_DELAY) {
          if (debugMode) {
            console.log('Brightness update throttled');
          }
          return;
        }
        
        // Validate brightness range
        const clampedValue = Math.max(
          BRIGHTNESS_DEFAULTS.MIN_BRIGHTNESS,
          Math.min(BRIGHTNESS_DEFAULTS.MAX_BRIGHTNESS, value)
        );
        
        set(
          {
            brightness: clampedValue,
            lastBrightnessUpdate: now,
          },
          false,
          'setBrightness'
        );
        
        if (debugMode) {
          console.log(`Local brightness set to: ${clampedValue}`);
        }
      },
      
      /**
       * Set brightness for a specific participant
       * @param {string} participantId - Participant identity
       * @param {number} value - Brightness value
       */
      setParticipantBrightness: (participantId, value) => {
        const { participantBrightness, debugMode } = get();
        
        // Validate brightness range
        const clampedValue = Math.max(
          BRIGHTNESS_DEFAULTS.MIN_BRIGHTNESS,
          Math.min(BRIGHTNESS_DEFAULTS.MAX_BRIGHTNESS, value)
        );
        
        // Create new Map to ensure immutability
        const newMap = new Map(participantBrightness);
        newMap.set(participantId, clampedValue);
        
        set(
          { participantBrightness: newMap },
          false,
          'setParticipantBrightness'
        );
        
        if (debugMode) {
          console.log(`Participant ${participantId} brightness set to: ${clampedValue}`);
        }
      },
    
      removeParticipantBrightness: (participantId) => {
        const { participantBrightness, debugMode } = get();
        
        if (!participantBrightness.has(participantId)) {
          return; // Participant not found, nothing to remove
        }
        
        const newMap = new Map(participantBrightness);
        newMap.delete(participantId);
        
        set(
          { participantBrightness: newMap },
          false,
          'removeParticipantBrightness'
        );
        
        if (debugMode) {
          console.log(`Removed brightness for participant: ${participantId}`);
        }
      },
      
      clearAllParticipantBrightness: () => {
        const { debugMode } = get();
        
        set(
          { participantBrightness: new Map() },
          false,
          'clearAllParticipantBrightness'
        );
        
        if (debugMode) {
          console.log('Cleared all participant brightness data');
        }
      },
      
      resetBrightness: () => {
        set(
          { brightness: BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS },
          false,
          'resetBrightness'
        );
      },

      toggleDebugMode: () => {
        set(
          (state) => ({ debugMode: !state.debugMode }),
          false,
          'toggleDebugMode'
        );
      },
      
      getParticipantBrightness: (participantId) => {
        const { participantBrightness } = get();
        return participantBrightness.get(participantId) || BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS;
      },

      isDefaultBrightness: () => {
        const { brightness } = get();
        return brightness === BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS;
      },

      getNonDefaultParticipants: () => {
        const { participantBrightness } = get();
        return Array.from(participantBrightness.entries())
          .filter(([, brightness]) => brightness !== BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS)
          .map(([participantId, brightness]) => ({ participantId, brightness }));
      },
    }),
    {
      name: 'brightness-store', // DevTools name
    }
  )
);

export const useLocalBrightness = () => {
  return useBrightnessStore((state) => ({
    brightness: state.brightness,
    setBrightness: state.setBrightness,
    resetBrightness: state.resetBrightness,
    isDefaultBrightness: state.isDefaultBrightness,
  }));
};

export const useParticipantBrightness = () => {
  return useBrightnessStore((state) => ({
    participantBrightness: state.participantBrightness,
    setParticipantBrightness: state.setParticipantBrightness,
    removeParticipantBrightness: state.removeParticipantBrightness,
    getParticipantBrightness: state.getParticipantBrightness,
  }));
};


export const useBrightnessActions = () => {
  return useBrightnessStore((state) => ({
    setBrightness: state.setBrightness,
    setParticipantBrightness: state.setParticipantBrightness,
    removeParticipantBrightness: state.removeParticipantBrightness,
    clearAllParticipantBrightness: state.clearAllParticipantBrightness,
    resetBrightness: state.resetBrightness,
  }));
};

/**
 * Hook for debugging and development
 */
export const useBrightnessDebug = () => {
  return useBrightnessStore((state) => ({
    debugMode: state.debugMode,
    toggleDebugMode: state.toggleDebugMode,
    getNonDefaultParticipants: state.getNonDefaultParticipants,
  }));
};

// Export the main store and constants
export { BRIGHTNESS_DEFAULTS };
export default useBrightnessStore;
