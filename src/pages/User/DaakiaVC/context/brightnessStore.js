import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * Production-ready Brightness Store using Zustand
 * 
 * Features:
 * - Centralized brightness state management
 * - Participant-specific brightness tracking
 * - Optimized re-rendering with selective subscriptions
 * - DevTools integration for debugging
 * - Anti-spam protection for network calls
 * - Immutable state updates
 */

const BRIGHTNESS_DEFAULTS = {
  DEFAULT_BRIGHTNESS: 100,
  MIN_BRIGHTNESS: 0,
  MAX_BRIGHTNESS: 200,
  THROTTLE_DELAY: 100, // ms - anti-spam protection
};

const useBrightnessStore = create(
  devtools(
    (set, get) => ({
      // ===== STATE =====
      
      /**
       * Local user's brightness setting
       */
      brightness: BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS,
      
      /**
       * Map of participant brightness values
       * Key: participant.identity (string)
       * Value: brightness value (number)
       */
      participantBrightness: new Map(),
      
      /**
       * Throttling state to prevent spam
       */
      lastBrightnessUpdate: 0,
      
      /**
       * Debug flag for development
       */
      debugMode: process.env.NODE_ENV === 'development',
      
      // ===== ACTIONS =====
      
      /**
       * Set local brightness value
       * @param {number} value - Brightness value (0-200)
       * @param {boolean} skipThrottle - Skip throttle check (for initialization)
       */
      setBrightness: (value, skipThrottle = false) => {
        const now = Date.now();
        const { lastBrightnessUpdate, debugMode } = get();
        
        // Throttle protection
        if (!skipThrottle && now - lastBrightnessUpdate < BRIGHTNESS_DEFAULTS.THROTTLE_DELAY) {
          if (debugMode) {
            console.log('Brightness update throttled');
          }
          return;
        }
        
        // Validate brightness range
        const clampedValue = Math.max(
          BRIGHTNESS_DEFAULTS.MIN_BRIGHTNESS,
          Math.min(BRIGHTNESS_DEFAULTS.MAX_BRIGHTNESS, value)
        );
        
        set(
          {
            brightness: clampedValue,
            lastBrightnessUpdate: now,
          },
          false,
          'setBrightness'
        );
        
        if (debugMode) {
          console.log(`Local brightness set to: ${clampedValue}`);
        }
      },
      
      /**
       * Set brightness for a specific participant
       * @param {string} participantId - Participant identity
       * @param {number} value - Brightness value
       */
      setParticipantBrightness: (participantId, value) => {
        const { participantBrightness, debugMode } = get();
        
        // Validate brightness range
        const clampedValue = Math.max(
          BRIGHTNESS_DEFAULTS.MIN_BRIGHTNESS,
          Math.min(BRIGHTNESS_DEFAULTS.MAX_BRIGHTNESS, value)
        );
        
        // Create new Map to ensure immutability
        const newMap = new Map(participantBrightness);
        newMap.set(participantId, clampedValue);
        
        set(
          { participantBrightness: newMap },
          false,
          'setParticipantBrightness'
        );
        
        if (debugMode) {
          console.log(`Participant ${participantId} brightness set to: ${clampedValue}`);
        }
      },
      
      /**
       * Remove participant brightness when they leave
       * @param {string} participantId - Participant identity
       */
      removeParticipantBrightness: (participantId) => {
        const { participantBrightness, debugMode } = get();
        
        if (!participantBrightness.has(participantId)) {
          return; // Participant not found, nothing to remove
        }
        
        const newMap = new Map(participantBrightness);
        newMap.delete(participantId);
        
        set(
          { participantBrightness: newMap },
          false,
          'removeParticipantBrightness'
        );
        
        if (debugMode) {
          console.log(`Removed brightness for participant: ${participantId}`);
        }
      },
      
      /**
       * Clear all participant brightness data (on disconnect)
       */
      clearAllParticipantBrightness: () => {
        const { debugMode } = get();
        
        set(
          { participantBrightness: new Map() },
          false,
          'clearAllParticipantBrightness'
        );
        
        if (debugMode) {
          console.log('Cleared all participant brightness data');
        }
      },
      
      /**
       * Reset local brightness to default
       */
      resetBrightness: () => {
        set(
          { brightness: BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS },
          false,
          'resetBrightness'
        );
      },
      
      /**
       * Toggle debug mode
       */
      toggleDebugMode: () => {
        set(
          (state) => ({ debugMode: !state.debugMode }),
          false,
          'toggleDebugMode'
        );
      },
      
      // ===== GETTERS =====
      
      /**
       * Get brightness for a specific participant
       * @param {string} participantId - Participant identity
       * @returns {number} Brightness value or default
       */
      getParticipantBrightness: (participantId) => {
        const { participantBrightness } = get();
        return participantBrightness.get(participantId) || BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS;
      },
      
      /**
       * Check if brightness is at default value
       * @returns {boolean}
       */
      isDefaultBrightness: () => {
        const { brightness } = get();
        return brightness === BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS;
      },
      
      /**
       * Get all participants with non-default brightness
       * @returns {Array} Array of {participantId, brightness} objects
       */
      getNonDefaultParticipants: () => {
        const { participantBrightness } = get();
        return Array.from(participantBrightness.entries())
          .filter(([, brightness]) => brightness !== BRIGHTNESS_DEFAULTS.DEFAULT_BRIGHTNESS)
          .map(([participantId, brightness]) => ({ participantId, brightness }));
      },
    }),
    {
      name: 'brightness-store', // DevTools name
    }
  )
);

// ===== CUSTOM HOOKS FOR OPTIMIZED SUBSCRIPTIONS =====

/**
 * Hook to subscribe only to local brightness changes
 * Prevents re-renders when participant brightness changes
 */
export const useLocalBrightness = () => {
  return useBrightnessStore((state) => ({
    brightness: state.brightness,
    setBrightness: state.setBrightness,
    resetBrightness: state.resetBrightness,
    isDefaultBrightness: state.isDefaultBrightness,
  }));
};

/**
 * Hook to subscribe only to participant brightness changes
 * Prevents re-renders when local brightness changes
 */
export const useParticipantBrightness = () => {
  return useBrightnessStore((state) => ({
    participantBrightness: state.participantBrightness,
    setParticipantBrightness: state.setParticipantBrightness,
    removeParticipantBrightness: state.removeParticipantBrightness,
    getParticipantBrightness: state.getParticipantBrightness,
  }));
};

/**
 * Hook for brightness management actions
 * Use this in components that need to manage brightness state
 */
export const useBrightnessActions = () => {
  return useBrightnessStore((state) => ({
    setBrightness: state.setBrightness,
    setParticipantBrightness: state.setParticipantBrightness,
    removeParticipantBrightness: state.removeParticipantBrightness,
    clearAllParticipantBrightness: state.clearAllParticipantBrightness,
    resetBrightness: state.resetBrightness,
  }));
};

/**
 * Hook for debugging and development
 */
export const useBrightnessDebug = () => {
  return useBrightnessStore((state) => ({
    debugMode: state.debugMode,
    toggleDebugMode: state.toggleDebugMode,
    getNonDefaultParticipants: state.getNonDefaultParticipants,
  }));
};

// Export the main store and constants
export { BRIGHTNESS_DEFAULTS };
export default useBrightnessStore;
