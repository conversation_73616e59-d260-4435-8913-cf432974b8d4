
import { create } from 'zustand';

export const useDeviceStore = create((set) => ({
  // --- State Properties --- //
  brightness: 100,
  outputVolume: 100,
  autoVideoOff: false,
  autoAudioOff: false,

  /**
   * A map to store the brightness of remote participants.
   * Key: participant.identity (string)
   * Value: brightness (number)
   */
  participantBrightness: new Map(),

  // --- Actions --- //
  setBrightness: (newBrightness) => set({ brightness: newBrightness }),
  setOutputVolume: (newVolume) => set({ outputVolume: newVolume }),
  toggleAutoVideoOff: () => set((state) => ({ autoVideoOff: !state.autoVideoOff })),
  setAutoAudioOff: (value) => set({ autoAudioOff: value }),

  /**
   * An action to set or update the brightness for a specific remote participant.
   * @param {string} participantId - The identity of the remote participant.
   * @param {number} newBrightness - The new brightness value for that participant.
   */
  setParticipantBrightness: (participantId, newBrightness) => {
    set((state) => ({
      participantBrightness: new Map(state.participantBrightness).set(participantId, newBrightness),
    }));
  },

  /**
   * An action to remove a participant's brightness data when they leave.
   * @param {string} participantId - The identity of the participant who left.
   */
  removeParticipantBrightness: (participantId) => {
    set((state) => {
      const newMap = new Map(state.participantBrightness);
      newMap.delete(participantId);
      return { participantBrightness: newMap };
    });
  },
}));

/**
 * --- HOW TO USE THIS STORE ---
 *
 * In any component that needs access to brightness, you can do the following.
 * This example shows how a component can subscribe to *only* the brightness
 * state, which prevents it from re-rendering when other state (like volume) changes.
 *
 * @example
 * import { useDeviceStore } from './deviceStore';
 *
 * function BrightnessControl() {
 *   // Select only the state and actions you need.
 *   const brightness = useDeviceStore((state) => state.brightness);
 *   const setBrightness = useDeviceStore((state) => state.setBrightness);
 *
 *   const handleChange = (event) => {
 *     setBrightness(Number(event.target.value));
 *   };
 *
 *   return (
 *     <div>
 *       <label>Brightness: {brightness}</label>
 *       <input
 *         type="range"
 *         min="0"
 *         max="200"
 *         value={brightness}
 *         onChange={handleChange}
 *       />
 *     </div>
 *   );
 * }
 */
