
# Zustand State Management Guidelines

This document outlines the features of Zustand and the rules for using it in this project.

---

## 1. Core Features of Zustand

We chose Zustand for several key reasons:

- **Minimal Boilerplate**: You don't need to wrap your app in context providers. It's just a hook.
- **High Performance**: Components only re-render when the *specific* state they subscribe to changes. This is a major advantage over the standard Context API and prevents unnecessary re-renders in our real-time application.
- **Un-opinionated and Simple**: The API is small and easy to learn. It feels like using a simple `useState` hook that is shared globally.
- **Modular by Design**: It encourages splitting state into small, independent, and reusable stores (slices). This is perfect for our feature-based architecture.
- **Access State Outside Components**: You can use Zustand stores in utility functions or anywhere outside of the React component tree, which is very powerful for handling logic that isn't tied to the UI.
- **Developer Tools**: It can be easily integrated with the Redux DevTools Extension for easy debugging of state changes.

---

## 2. Rules for Creating and Using Stores

To keep our state management consistent and predictable, please follow these rules.

### Rule 1: File Location and Naming

- **Location**: All Zustand store files **MUST** be created inside the `/src/pages/User/DaakiaVC/context/` directory.
- **Naming Convention**: Store files **MUST** be named using the pattern `use[Feature]Store.js` (e.g., `useDeviceStore.js`, `useMeetingStore.js`).

### Rule 2: Store Structure (One Feature, One Store)

- Each file should represent a single "slice" of the application state, related to a specific feature.
- A store file **MUST** contain all the state properties and the actions that modify those properties for its feature.
- **DO NOT** create a single, monolithic store file for the entire application.

**Example:** All state related to chat (messages, unread counts, etc.) should go in `useChatStore.js`.

### Rule 3: Creating a Store

- Use the `create` function from Zustand.
- The `create` function takes a callback that receives `set` and `get`.
  - `set`: The function used to update state properties.
  - `get`: The function used to access the current state inside an action (useful for computed properties).

```javascript
import { create } from 'zustand';

export const useMyStore = create((set, get) => ({
  // State properties go here
  myValue: 0,

  // Actions (functions that update state) go here
  increment: () => set((state) => ({ myValue: state.myValue + 1 })),

  // Computed values (functions that derive state)
  getMyValueDoubled: () => get().myValue * 2,
}));
```

### Rule 4: Using a Store in a Component (Performance is Key)

- To access state, import the store and use it like a hook.
- **IMPORTANT**: For performance, always subscribe to the smallest piece of state you need. This prevents your component from re-rendering unnecessarily.

**DO THIS (Subscribing to a single value):**
```javascript
const myValue = useMyStore((state) => state.myValue);
const increment = useMyStore((state) => state.increment);
```

**AVOID THIS (Subscribing to the entire store):**
```javascript
// Avoid this! It will cause the component to re-render whenever
// ANY property in the store changes.
const { myValue, increment } = useMyStore();
```

### Rule 5: Handling Asynchronous Actions

- Actions can be `async`.
- When performing an async action (like an API call), always manage the loading state within the same action.

```javascript
// Inside create()

  isLoading: false,
  fetchData: async () => {
    set({ isLoading: true }); // Set loading to true before the call
    const response = await fetch('/api/data');
    const data = await response.json();
    set({ data: data, isLoading: false }); // Update state and set loading to false
  },
```

### Rule 6: No Default Exports

- Always use named exports for your stores (`export const useMyStore = ...`) to maintain consistency.

