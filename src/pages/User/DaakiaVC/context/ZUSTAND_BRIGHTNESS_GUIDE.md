# Zustand Brightness Store - Production Guide

## Overview

This guide demonstrates how to use the Zustand brightness store for production-level state management in the DaakiaVC video conferencing application.

## Key Features

✅ **Centralized State Management** - Single source of truth for all brightness data  
✅ **Optimized Re-rendering** - Selective subscriptions prevent unnecessary re-renders  
✅ **Anti-spam Protection** - Throttling prevents network flooding  
✅ **DevTools Integration** - Debug state changes in development  
✅ **Immutable Updates** - Safe state mutations  
✅ **TypeScript Ready** - Easy to add type definitions  

## Architecture Benefits

### 1. **Selective Subscriptions**
Components only re-render when their specific data changes:
```javascript
// Only re-renders when LOCAL brightness changes
const { brightness, setBrightness } = useLocalBrightness();

// Only re-renders when PARTICIPANT brightness changes  
const { participantBrightness } = useParticipantBrightness();
```

### 2. **Performance Optimized**
- Throttling prevents spam (100ms default)
- Immutable Map updates for participant data
- Minimal re-renders with targeted subscriptions

### 3. **Production Ready**
- Error boundaries with value clamping
- Debug mode for development
- DevTools integration
- Clean separation of concerns

## Usage Examples

### 1. Settings Modal (Brightness Slider)

```javascript
import { useLocalBrightness } from '../context/brightnessStore';

function BrightnessSlider() {
  const { brightness, setBrightness, isDefaultBrightness } = useLocalBrightness();
  
  const handleChange = (event) => {
    setBrightness(Number(event.target.value));
  };
  
  return (
    <div>
      <label>Brightness: {brightness}%</label>
      <input
        type="range"
        min="0"
        max="200"
        value={brightness}
        onChange={handleChange}
      />
      {!isDefaultBrightness() && (
        <span>Modified from default</span>
      )}
    </div>
  );
}
```

### 2. Participant Tile (Apply Brightness Filter)

```javascript
import { useParticipantBrightness } from '../context/brightnessStore';

function ParticipantTile({ participant }) {
  const { getParticipantBrightness } = useParticipantBrightness();
  
  const brightness = getParticipantBrightness(participant.identity);
  
  const videoStyle = {
    filter: `brightness(${brightness}%)`,
  };
  
  return (
    <div className="participant-tile">
      <video style={videoStyle} />
      <span>{participant.name}</span>
    </div>
  );
}
```

### 3. Video Conference Manager (LiveKit Integration)

```javascript
import { useBrightnessActions } from '../context/brightnessStore';
import { useEffect } from 'react';

function VideoConferenceManager({ room }) {
  const {
    setParticipantBrightness,
    removeParticipantBrightness,
    clearAllParticipantBrightness
  } = useBrightnessActions();
  
  useEffect(() => {
    const handleDataReceived = (payload, participant) => {
      try {
        const data = JSON.parse(payload);
        if (data.type === 'brightness') {
          setParticipantBrightness(participant.identity, data.value);
        }
      } catch (error) {
        console.error('Failed to parse brightness data:', error);
      }
    };
    
    const handleParticipantDisconnected = (participant) => {
      removeParticipantBrightness(participant.identity);
    };
    
    const handleDisconnected = () => {
      clearAllParticipantBrightness();
    };
    
    room.on('dataReceived', handleDataReceived);
    room.on('participantDisconnected', handleParticipantDisconnected);
    room.on('disconnected', handleDisconnected);
    
    return () => {
      room.off('dataReceived', handleDataReceived);
      room.off('participantDisconnected', handleParticipantDisconnected);
      room.off('disconnected', handleDisconnected);
    };
  }, [room, setParticipantBrightness, removeParticipantBrightness, clearAllParticipantBrightness]);
  
  return null; // This component manages events only
}
```

### 4. Brightness Network Manager (RPC/Data Channel)

```javascript
import useBrightnessStore, { useLocalBrightness } from '../context/brightnessStore';

function BrightnessNetworkManager({ room }) {
  const { brightness } = useLocalBrightness();
  
  // Send brightness to all participants when it changes
  useEffect(() => {
    if (!room || brightness === 100) return; // Don't send default values
    
    const brightnessData = {
      type: 'brightness',
      value: brightness,
      timestamp: Date.now()
    };
    
    // Send via data channel
    room.localParticipant.publishData(
      JSON.stringify(brightnessData),
      { reliable: true }
    );
    
  }, [brightness, room]);
  
  return null;
}
```

## Best Practices

### 1. **Component Organization**
```
components/
├── BrightnessSlider.js      // Uses useLocalBrightness
├── ParticipantTile.js       // Uses useParticipantBrightness  
├── VideoConference.js       // Uses useBrightnessActions
└── SettingsModal.js         // Uses useLocalBrightness
```

### 2. **Error Handling**
```javascript
// The store automatically clamps values
setBrightness(300); // Automatically becomes 200 (max)
setBrightness(-50); // Automatically becomes 0 (min)
```

### 3. **Development Debugging**
```javascript
import { useBrightnessDebug } from '../context/brightnessStore';

function DebugPanel() {
  const { debugMode, toggleDebugMode, getNonDefaultParticipants } = useBrightnessDebug();
  
  if (!debugMode) return null;
  
  return (
    <div className="debug-panel">
      <h3>Brightness Debug</h3>
      <button onClick={toggleDebugMode}>Toggle Debug</button>
      <pre>{JSON.stringify(getNonDefaultParticipants(), null, 2)}</pre>
    </div>
  );
}
```

### 4. **Performance Monitoring**
```javascript
// The store includes throttling - check console in debug mode
// "Brightness update throttled" indicates anti-spam is working
```

## Migration from Existing Code

### Before (Props Drilling):
```javascript
// index.page.js
const [brightness, setBrightness] = useState(100);
const [participantBrightness, setParticipantBrightness] = useState(new Map());

// Pass down through multiple components...
<VideoConference 
  brightness={brightness}
  setBrightness={setBrightness}
  participantBrightness={participantBrightness}
  setParticipantBrightness={setParticipantBrightness}
/>
```

### After (Zustand):
```javascript
// Any component can access brightness state directly
import { useLocalBrightness } from '../context/brightnessStore';

function AnyComponent() {
  const { brightness, setBrightness } = useLocalBrightness();
  // Use directly - no props needed!
}
```

## Advanced Usage

### Custom Middleware
```javascript
// Add persistence middleware
import { persist } from 'zustand/middleware';

const useBrightnessStore = create(
  persist(
    devtools(/* store definition */),
    { name: 'brightness-storage' }
  )
);
```

### Computed Values
```javascript
// Add computed selectors
export const useBrightnessStats = () => {
  return useBrightnessStore((state) => {
    const totalParticipants = state.participantBrightness.size;
    const modifiedCount = state.getNonDefaultParticipants().length;
    return { totalParticipants, modifiedCount };
  });
};
```

This architecture provides a solid foundation for brightness management that scales with your application needs while maintaining excellent performance and developer experience.
