import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import React, { useCallback } from "react";
import { ParticipantService } from "../services/ParticipantServices";
import { generateAvatar, getParticipantColor } from "../utils/helper";

export default function AllowParticipant({
  participantName,
  requestId,
  id,
  setShowToast,
  coHostToken,
  localParticipant,
  setToastNotification,
  setToastStatus,
  participantColors = new Map(),
  participantIdentity,
  removeParticipantFromLobby
}) {
  const avatarName = generateAvatar(participantName);

  // Helper function to get participant color with fallback
  const getAvatarColor = (identity) => {
    if (participantColors && identity) {
      const color = getParticipantColor(participantColors, identity);
      return color || "#fd4563"; // fallback to original color
    }
    return "#fd4563"; // fallback to original color
  };

  const handleStatus = useCallback(
    async (status) => {
      try {
        const response = await ParticipantService.lobbyParticipantStatusUpdate(
          id,
          requestId,
          status,
          coHostToken,
          localParticipant?.participantInfo
        );

        if (response.success === 0) {
          console.log("Error updating participant status", response);
        }

        // Use the removeParticipantFromLobby function
        if (removeParticipantFromLobby) {
          removeParticipantFromLobby(requestId);
        }
      } catch (error) {
        setToastNotification(error.message);
        setShowToast(true);
        setToastStatus("error");

        // console.error("Error updating participant status", error);
      }
    },
    [id, requestId, coHostToken, localParticipant, removeParticipantFromLobby, setToastNotification, setShowToast, setToastStatus]
  );

  return (
    <div className="pwj-toast">
      <p>Someone wants to join!</p>
      <div className="pwj-name-card">
        <Avatar style={{ backgroundColor: getAvatarColor(participantIdentity), verticalAlign: "middle" }}>
          {avatarName}
        </Avatar>
        <span>{participantName}</span>
      </div>
      <div className="pwj-actions">
        <Button type="primary" onClick={() => handleStatus(true)}>
          Allow
        </Button>
        <Button type="text" onClick={() => handleStatus(false)}>
          Deny
        </Button>
      </div>
    </div>
  );
}
